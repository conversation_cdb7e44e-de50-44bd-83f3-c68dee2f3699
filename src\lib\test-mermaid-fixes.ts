/**
 * Test file to validate Mermaid diagram fixes
 * This can be used to test common AI-generated syntax issues
 */

import { fixAiGeneratedMermaidScript, fixMermaidSyntax } from './mermaid-utils';

// Common problematic AI-generated Mermaid scripts
const testCases = [
  {
    name: 'Graph with semicolon',
    input: `graph TD;
    A[Start] --> B[Process]
    B --> C[End]`,
    expectedFixes: ['Converted \'graph TD;\' to \'flowchart TD\'']
  },
  {
    name: 'Missing diagram declaration',
    input: `A[Start] --> B[Process]
    B --> C[End]`,
    expectedFixes: ['Added missing flowchart declaration']
  },
  {
    name: 'Markdown code blocks',
    input: `\`\`\`mermaid
    flowchart TD
    A[Start] --> B[End]
    \`\`\``,
    expectedFixes: ['Removed markdown code block wrapper']
  },
  {
    name: 'Spaced arrows',
    input: `flowchart TD
    A[Start] - - > B[Process]
    B = = > C[End]`,
    expectedFixes: ['Fixed spaced solid arrows', 'Fixed spaced thick arrows']
  },
  {
    name: 'Unquoted labels with spaces',
    input: `flowchart TD
    A[User Interface] --> B[Business Logic Layer]
    B --> C[Data Access Layer]`,
    expectedFixes: []
  },
  {
    name: 'Missing subgraph end',
    input: `flowchart TD
    A[Start] --> B[Process]
    subgraph "Processing"
        B --> C[Step 1]
        C --> D[Step 2]
    B --> E[End]`,
    expectedFixes: ['Added 1 missing \'end\' statement(s) for subgraphs']
  }
];

/**
 * Test the AI-generated Mermaid script fixes
 */
export function testMermaidFixes(): void {
  console.log('🧪 Testing Mermaid diagram fixes...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log('Input:', testCase.input.replace(/\n/g, '\\n'));
    
    const result = fixAiGeneratedMermaidScript(testCase.input);
    
    console.log('Output:', result.fixedScript.replace(/\n/g, '\\n'));
    console.log('Changes:', result.changes);
    console.log('Valid:', result.isValid);
    
    // Check if expected fixes were applied
    const hasExpectedFixes = testCase.expectedFixes.every(expectedFix =>
      result.changes.some(change => change.includes(expectedFix) || expectedFix.includes(change))
    );
    
    if (hasExpectedFixes || testCase.expectedFixes.length === 0) {
      console.log('✅ PASSED\n');
      passedTests++;
    } else {
      console.log('❌ FAILED - Expected fixes not found');
      console.log('Expected:', testCase.expectedFixes);
      console.log('Actual:', result.changes);
      console.log('');
    }
  });
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the output above.');
  }
}

/**
 * Test with a realistic AI-generated architecture diagram
 */
export function testRealisticExample(): void {
  console.log('\n🏗️ Testing realistic architecture example...\n');
  
  const realisticInput = `graph TD;
    User[User Interface] - - > Gateway[API Gateway]
    Gateway = = > Auth[Authentication Service]
    Gateway --> Business[Business Logic Layer]
    
    subgraph Data Layer
        DB[(Primary Database)]
        Cache[(Redis Cache)]
        Files[File Storage]
    
    Business --> DB
    Business --> Cache
    Business --> Files
    
    subgraph External Services
        Email[Email Service]
        Payment[Payment Gateway]
    
    Business --> Email
    Business --> Payment`;
  
  console.log('Input script:');
  console.log(realisticInput);
  
  const result = fixAiGeneratedMermaidScript(realisticInput);
  
  console.log('\nFixed script:');
  console.log(result.fixedScript);
  
  console.log('\nChanges applied:');
  result.changes.forEach(change => console.log(`- ${change}`));
  
  console.log(`\nScript is valid: ${result.isValid}`);
  if (result.error) {
    console.log(`Error: ${result.error}`);
  }
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).testMermaidFixes = testMermaidFixes;
  (window as any).testRealisticExample = testRealisticExample;
}
