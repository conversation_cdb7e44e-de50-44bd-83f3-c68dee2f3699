
'use server';
/**
 * @fileOverview Generates Mermaid.js scripts for architecture diagrams.
 *
 * - generateArchitectureDiagram - A function that generates a Mermaid script based on a description and diagram type.
 * - GenerateArchitectureDiagramInput - The input type for the generateArchitectureDiagram function.
 * - GenerateArchitectureDiagramOutput - The return type for the generateArchitectureDiagram function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { fixAiGeneratedMermaidScript } from '@/lib/mermaid-utils';

const GenerateArchitectureDiagramInputSchema = z.object({
  architectureDescription: z
    .string()
    .describe('A description of the system or solution for which to generate an architecture diagram.'),
  diagramType: z.enum(['conceptual', 'reference'])
    .describe('The type of architecture diagram to generate: "conceptual" or "reference".'),
});
export type GenerateArchitectureDiagramInput = z.infer<typeof GenerateArchitectureDiagramInputSchema>;

const GenerateArchitectureDiagramOutputSchema = z.object({
  mermaidScript: z.string().describe('The generated Mermaid.js script for the architecture diagram.'),
});
export type GenerateArchitectureDiagramOutput = z.infer<typeof GenerateArchitectureDiagramOutputSchema>;

export async function generateArchitectureDiagram(input: GenerateArchitectureDiagramInput): Promise<GenerateArchitectureDiagramOutput> {
  return generateArchitectureDiagramFlow(input);
}

const architectureVisualizerPrompt = ai.definePrompt({
  name: 'architectureVisualizerPrompt',
  input: {schema: GenerateArchitectureDiagramInputSchema},
  output: {schema: GenerateArchitectureDiagramOutputSchema},
  prompt: `You are an expert system architect and AI assistant specialized in creating architecture diagrams using Mermaid.js syntax.
Your task is to generate a valid Mermaid.js script based on the provided architecture description and the desired diagram type.

Architecture Description:
{{{architectureDescription}}}

Diagram Type: {{{diagramType}}}

CRITICAL SYNTAX REQUIREMENTS:
1. Always start with "flowchart TD" or "flowchart LR" (NOT "graph")
2. Use proper node syntax: A[Label], B(Label), C{Decision}, D((Circle)), E[[Subroutine]]
3. Use proper arrow syntax: --> (solid), -.-> (dotted), ==> (thick)
4. Quote labels with spaces: A["Multi Word Label"]
5. Use subgraphs for grouping: subgraph "Group Name"

EXAMPLES OF CORRECT SYNTAX:

Basic Web Application:
flowchart TD
    A[User] --> B[Load Balancer]
    B --> C[Web Server 1]
    B --> D[Web Server 2]
    C --> E[(Database)]
    D --> E

Microservices Architecture:
flowchart TD
    A[Client] --> B[API Gateway]
    B --> C[Auth Service]
    B --> D[User Service]
    B --> E[Order Service]

    subgraph "Data Layer"
        F[(User DB)]
        G[(Order DB)]
    end

    D --> F
    E --> G

Instructions for Diagram Types:
- Conceptual Diagram: Focus on the "what." Show the overall system structure, key concepts, main components, and their high-level relationships. Use clear, high-level labels.
- Reference Architecture Diagram: Focus on "how" components interact. Show key components, their connections, data flows, and dependencies. Include specific technologies when relevant.

VALIDATION CHECKLIST:
- Start with "flowchart TD" or "flowchart LR"
- All nodes have proper syntax (brackets, parentheses, etc.)
- All arrows use proper syntax (-->, -.-, ==>, etc.)
- Labels with spaces are quoted
- Subgraphs are properly closed with "end"
- No trailing semicolons on lines

Generate ONLY the Mermaid.js script. Do not include explanations, titles, or markdown formatting.
The output must be directly usable as a Mermaid script and render without errors.
`,
  config: {
    safetySettings: [
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_ONLY_HIGH',
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
});

const generateArchitectureDiagramFlow = ai.defineFlow(
  {
    name: 'generateArchitectureDiagramFlow',
    inputSchema: GenerateArchitectureDiagramInputSchema,
    outputSchema: GenerateArchitectureDiagramOutputSchema,
  },
  async (input) => {
    const {output} = await architectureVisualizerPrompt(input);

    if (!output?.mermaidScript) {
        throw new Error('AI failed to generate a Mermaid script.');
    }

    // Log the raw output for debugging
    console.log('[Architecture Visualizer] Raw AI output:', output.mermaidScript);

    // Clean up common formatting issues
    let script = output.mermaidScript.trim();

    // Remove markdown code blocks
    if (script.startsWith('```mermaid')) {
        script = script.substring('```mermaid'.length);
    }
    if (script.startsWith('```')) {
        script = script.substring('```'.length);
    }
    if (script.endsWith('```')) {
        script = script.substring(0, script.length - '```'.length);
    }

    // Remove any leading/trailing whitespace
    script = script.trim();

    // Apply AI-specific fixes first
    const aiFixResult = fixAiGeneratedMermaidScript(script);
    if (aiFixResult.changes.length > 0) {
      console.log('[Architecture Visualizer] Applied AI-specific fixes:', aiFixResult.changes);
      script = aiFixResult.fixedScript;
    }

    // Additional validation and fixes
    script = validateAndFixMermaidScript(script, input);

    if (!script) {
        throw new Error('AI generated an empty or invalid Mermaid script after cleanup.');
    }

    console.log('[Architecture Visualizer] Final processed script:', script);

    return { mermaidScript: script };
  }
);

/**
 * Validates and fixes common issues in AI-generated Mermaid scripts
 */
function validateAndFixMermaidScript(script: string, input: GenerateArchitectureDiagramInput): string {
  if (!script) return '';

  let fixedScript = script;
  const fixes: string[] = [];

  // 1. Ensure proper diagram declaration
  const firstLine = fixedScript.split('\n')[0].trim();
  if (!firstLine.toLowerCase().startsWith('flowchart') && !firstLine.toLowerCase().startsWith('graph')) {
    fixedScript = `flowchart TD\n${fixedScript}`;
    fixes.push('Added missing flowchart declaration');
  }

  // 2. Convert 'graph' to 'flowchart' for better compatibility
  if (firstLine.toLowerCase().startsWith('graph ')) {
    fixedScript = fixedScript.replace(/^graph\s+/i, 'flowchart ');
    fixes.push('Converted graph to flowchart');
  }

  // 3. Ensure direction is specified
  const lines = fixedScript.split('\n');
  const declarationLine = lines[0].trim();
  const parts = declarationLine.split(/\s+/);
  if (parts.length === 1 && parts[0].toLowerCase() === 'flowchart') {
    lines[0] = 'flowchart TD';
    fixedScript = lines.join('\n');
    fixes.push('Added default TD direction');
  }

  // 4. Fix common arrow syntax issues
  fixedScript = fixedScript.replace(/\s*-\s*-\s*>/g, ' --> ');
  fixedScript = fixedScript.replace(/\s*=\s*=\s*>/g, ' ==> ');
  fixedScript = fixedScript.replace(/\s*-\s*\.\s*-\s*>/g, ' -.-> ');

  // 5. Remove trailing semicolons (modern Mermaid doesn't need them)
  fixedScript = fixedScript.split('\n').map(line => {
    return line.replace(/;\s*$/, '');
  }).join('\n');

  // 6. Basic subgraph validation
  const subgraphCount = (fixedScript.match(/subgraph\s+/gi) || []).length;
  const endCount = (fixedScript.match(/^\s*end\s*$/gm) || []).length;
  if (subgraphCount > endCount) {
    const missingEnds = subgraphCount - endCount;
    fixedScript += '\n' + 'end\n'.repeat(missingEnds);
    fixes.push(`Added ${missingEnds} missing 'end' statements`);
  }

  // 7. Fallback to template if script is still invalid
  if (!isBasicMermaidValid(fixedScript)) {
    console.warn('[Architecture Visualizer] Generated script appears invalid, using fallback template');
    fixedScript = generateFallbackTemplate(input);
    fixes.push('Used fallback template due to validation failure');
  }

  if (fixes.length > 0) {
    console.log('[Architecture Visualizer] Applied fixes:', fixes);
  }

  return fixedScript;
}

/**
 * Basic validation to check if Mermaid script has required elements
 */
function isBasicMermaidValid(script: string): boolean {
  if (!script || script.trim().length < 10) return false;

  const lines = script.split('\n').map(l => l.trim()).filter(l => l.length > 0);
  if (lines.length < 2) return false;

  // Check for diagram declaration
  const firstLine = lines[0].toLowerCase();
  if (!firstLine.startsWith('flowchart') && !firstLine.startsWith('graph')) return false;

  // Check for at least one node or connection
  const hasNodes = lines.some(line =>
    line.includes('[') || line.includes('(') || line.includes('{') || line.includes('-->')
  );

  return hasNodes;
}

/**
 * Generates a fallback template based on the input description
 */
function generateFallbackTemplate(input: GenerateArchitectureDiagramInput): string {
  const isConceptual = input.diagramType === 'conceptual';

  if (isConceptual) {
    return `flowchart TD
    A[User Requirements] --> B[System Analysis]
    B --> C[Solution Design]
    C --> D[Implementation Plan]
    D --> E[Deployment Strategy]

    subgraph "Core Components"
        F[Component 1]
        G[Component 2]
        H[Component 3]
    end

    C --> F
    C --> G
    C --> H`;
  } else {
    return `flowchart TD
    A[Client Layer] --> B[API Gateway]
    B --> C[Authentication Service]
    B --> D[Business Logic Layer]

    subgraph "Data Layer"
        E[(Primary Database)]
        F[(Cache)]
        G[(File Storage)]
    end

    D --> E
    D --> F
    D --> G

    subgraph "External Services"
        H[Third Party API]
        I[Monitoring Service]
    end

    D --> H
    B --> I`;
  }
}

