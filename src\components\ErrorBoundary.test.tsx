import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ErrorBoundary } from './ErrorBoundary';

// Extend Jest matchers for better TypeScript support
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
    }
  }
  
  // Add resetErrorState to Window interface
  interface Window {
    resetErrorState?: () => void;
  }
}

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('ErrorBoundary', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });
  afterAll(() => {
    console.error = originalError;
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('renders error UI when there is an error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. This has been logged and we\'ll look into it.')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /go home/i })).toBeInTheDocument();
  });

  it('renders custom fallback when provided', () => {
    const customFallback = <div>Custom error message</div>;
    
    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Custom error message')).toBeInTheDocument();
    expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
  });

  it('calls onError callback when error occurs', () => {
    const onError = jest.fn();
    
    render(
      <ErrorBoundary onError={onError}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    );
  });

  it('resets error state when retry button is clicked', () => {
    // We need a key to force remount of the error boundary
    const TestErrorBoundary = ({ shouldThrow = false, boundaryKey = '1' }) => {
      return (
        <ErrorBoundary key={boundaryKey}>
          {shouldThrow ? <ThrowError shouldThrow={true} /> : <div>No error</div>}
        </ErrorBoundary>
      );
    };
    
    // Render with a component that throws
    const { rerender } = render(<TestErrorBoundary shouldThrow={true} />);
    
    // Verify error UI is shown
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    
    // Find and click retry button
    const tryAgainButton = screen.getByRole('button', { name: /try again/i });
    fireEvent.click(tryAgainButton);
    
    // Force remount with a new key and no error
    rerender(<TestErrorBoundary shouldThrow={false} boundaryKey="2" />);
    
    // Now we should see the no error state
    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('shows development error details in development mode', () => {
    // Save original NODE_ENV
    const originalNodeEnv = process.env.NODE_ENV;
    
    // Mock NODE_ENV before rendering
    jest.resetModules();
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'development',
      configurable: true,
    });
    
    // Force a clean render with the mocked environment
    const { unmount } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    // Look for error details with a more flexible query
    const errorDetailsElement = screen.getByText(/error details/i);
    expect(errorDetailsElement).toBeInTheDocument();
    
    // Check for error message
    expect(screen.getByText('Test error')).toBeInTheDocument();
    
    // Clean up
    unmount();
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalNodeEnv,
      configurable: true,
    });
  });
});