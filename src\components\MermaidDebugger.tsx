"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { fixAiGeneratedMermaidScript, fixMermaidSyntax } from '@/lib/mermaid-utils';
import { generateArchitectureDiagramAction } from '@/app/actions';

interface MermaidDebuggerProps {
  className?: string;
}

export const MermaidDebugger: React.FC<MermaidDebuggerProps> = ({ className }) => {
  const [input, setInput] = useState('');
  const [aiOutput, setAiOutput] = useState('');
  const [fixedOutput, setFixedOutput] = useState('');
  const [changes, setChanges] = useState<string[]>([]);
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [error, setError] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateWithAI = async () => {
    if (!input.trim()) return;
    
    setIsGenerating(true);
    setError('');
    
    try {
      const result = await generateArchitectureDiagramAction({
        architectureDescription: input,
        diagramType: 'conceptual'
      });
      
      setAiOutput(result.mermaidScript);
      
      // Automatically apply fixes
      const fixResult = fixAiGeneratedMermaidScript(result.mermaidScript);
      setFixedOutput(fixResult.fixedScript);
      setChanges(fixResult.changes);
      setIsValid(fixResult.isValid);
      if (fixResult.error) {
        setError(fixResult.error);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to generate diagram');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFixManualInput = () => {
    if (!aiOutput.trim()) return;
    
    const fixResult = fixAiGeneratedMermaidScript(aiOutput);
    setFixedOutput(fixResult.fixedScript);
    setChanges(fixResult.changes);
    setIsValid(fixResult.isValid);
    if (fixResult.error) {
      setError(fixResult.error);
    } else {
      setError('');
    }
  };

  const testCases = [
    {
      name: 'E-commerce Platform',
      description: 'A scalable e-commerce platform with microservices architecture, payment processing, and user management'
    },
    {
      name: 'Data Analytics Pipeline',
      description: 'Real-time data processing pipeline with streaming data, ETL processes, and machine learning components'
    },
    {
      name: 'Cloud-Native Application',
      description: 'Containerized application with Kubernetes orchestration, API gateway, and distributed database'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>Mermaid Diagram Debugger</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Architecture Description
            </label>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Describe the architecture you want to visualize..."
              className="min-h-[100px]"
            />
            <div className="flex gap-2 mt-2">
              <Button 
                onClick={handleGenerateWithAI}
                disabled={!input.trim() || isGenerating}
              >
                {isGenerating ? 'Generating...' : 'Generate with AI'}
              </Button>
              {testCases.map((testCase, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setInput(testCase.description)}
                >
                  {testCase.name}
                </Button>
              ))}
            </div>
          </div>

          {aiOutput && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  AI Generated Output
                </label>
                <Textarea
                  value={aiOutput}
                  onChange={(e) => setAiOutput(e.target.value)}
                  className="min-h-[200px] font-mono text-sm"
                />
                <Button 
                  onClick={handleFixManualInput}
                  className="mt-2"
                  size="sm"
                >
                  Apply Fixes
                </Button>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Fixed Output
                  {isValid !== null && (
                    <Badge variant={isValid ? "default" : "destructive"} className="ml-2">
                      {isValid ? 'Valid' : 'Invalid'}
                    </Badge>
                  )}
                </label>
                <Textarea
                  value={fixedOutput}
                  readOnly
                  className="min-h-[200px] font-mono text-sm bg-muted"
                />
              </div>
            </div>
          )}

          {changes.length > 0 && (
            <div>
              <label className="text-sm font-medium mb-2 block">
                Changes Applied ({changes.length})
              </label>
              <div className="space-y-1">
                {changes.map((change, index) => (
                  <div key={index} className="text-sm p-2 bg-muted rounded">
                    {change}
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div className="text-sm p-3 bg-destructive/10 text-destructive rounded">
              <strong>Error:</strong> {error}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
