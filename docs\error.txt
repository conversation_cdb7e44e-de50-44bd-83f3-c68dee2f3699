npm test -- src/components/ErrorBoundary.test.tsx

> nextn@0.1.0 test
> jest src/components/ErrorBoundary.test.tsx

 FAIL  src/components/ErrorBoundary.test.tsx
  ErrorBoundary
    √ renders children when there is no error (16 ms)
    √ renders error UI when there is an error (61 ms)
    √ renders custom fallback when provided (3 ms)
    √ calls onError callback when error occurs (4 ms)
    √ resets error state when retry button is clicked (13 ms)
    × shows development error details in development mode (5 ms)

  ● ErrorBoundary › shows development error details in development mode

    TestingLibraryElementError: Unable to find an element with the text: /error details/i. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

    Ignored nodes: comments, script, style
    <body>
      <div>
        <div
          class="min-h-screen bg-background flex items-center justify-center p-4"
        >
          <div
            class="rounded-lg border bg-card text-card-foreground shadow-sm w-full max-w-md"
          >
            <div
              class="flex flex-col space-y-1.5 p-6 text-center"
            >
              <div
                class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10"
              >
                AlertTriangle
              </div>
              <div
                class="font-semibold tracking-tight text-xl"
              >
                Something went wrong
              </div>
              <div
                class="text-sm text-muted-foreground"
              >
                An unexpected error occurred. This has been logged and we'll look into it.
              </div>
            </div>
            <div
              class="p-6 pt-0 space-y-4"
            >
              <div
                class="flex flex-col sm:flex-row gap-2"
              >
                <button
                  class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 flex-1"
                >
                  RefreshCw
                  Try Again
                </button>
                <button
                  class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 flex-1"
                >
                  Home
                  Go Home
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </body>

      134 |
      135 |     // Look for error details with a more flexible query
    > 136 |     const errorDetailsElement = screen.getByText(/error details/i);
          |                                        ^
      137 |     expect(errorDetailsElement).toBeInTheDocument();
      138 |     
      139 |     // Check for error message

      at Object.getElementError (node_modules/@testing-library/dom/dist/config.js:37:19)
      at node_modules/@testing-library/dom/dist/query-helpers.js:76:38
      at node_modules/@testing-library/dom/dist/query-helpers.js:52:17
      at node_modules/@testing-library/dom/dist/query-helpers.js:95:19
      at Object.<anonymous> (src/components/ErrorBoundary.test.tsx:136:40)

Test Suites: 1 failed, 1 total                                                                                                                                                                                         
Tests:       1 failed, 5 passed, 6 total                                                                                                                                                                               
Snapshots:   0 total
Time:        1.441 s
Ran all test suites matching /src\\components\\ErrorBoundary.test.tsx/i.