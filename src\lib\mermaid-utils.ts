/**
 * Utility functions for working with Mermaid diagrams
 */

export interface MermaidFixResult {
  fixedScript: string;
  changes: string[];
  isValid: boolean;
  error?: string;
}

const DIAGRAM_TYPES = [
  'graph', 'flowchart', // Common aliases
  'sequenceDiagram', 'classDiagram', 'stateDiagram', 'stateDiagram-v2',
  'erDiagram', 'gantt', 'pie', 'gitGraph', 'journey', 'C4Context', 'mindmap',
  'timeline', 'zenuml', 'sankey-beta', 'quadrantChart', 'requirementDiagram', 'xychart-beta'
];

function escapeStringForMermaid(str: string): string {
  // Escape quotes and other special characters for Mermaid labels
  return str.replace(/"/g, '#quot;').replace(/'/g, '#apos;');
}

export function fixMermaidSyntax(script: string): MermaidFixResult {
  if (typeof script !== 'string') {
    return {
      fixedScript: '',
      changes: [],
      isValid: false,
      error: 'Invalid input: <PERSON>rip<PERSON> must be a string.',
    };
  }

  let fixedScript = script.replace(/\r\n/g, '\n').trim(); // Normalize line endings and trim
  const changes: string[] = [];
  const addChange = (description: string, condition = true) => {
    if (condition) changes.push(description);
  };

  if (fixedScript.startsWith('|')) {
    fixedScript = fixedScript.substring(1);
    addChange("Removed leading '|' character from script.");
  }
  const originalScriptForChangeDetection = fixedScript;
  let lines = fixedScript.split('\n'); // Declare and initialize lines here

  // 1. Handle Empty or Minimal Script
  if (!fixedScript) {
    fixedScript = 'flowchart TD\n    A[Start] --> B[End]';
    addChange('Script was empty. Added a default diagram.');
    return { fixedScript, changes, isValid: true };
  }

  // 2. Ensure Diagram Declaration
  const firstLine = fixedScript.split('\n')[0].trim().toLowerCase();
  let hasDeclaration = DIAGRAM_TYPES.some(type => firstLine.startsWith(type.toLowerCase()));

  if (!hasDeclaration) {
    let detectedType = 'flowchart TD'; // Default
    if (fixedScript.includes('participant') || fixedScript.includes('actor')) detectedType = 'sequenceDiagram';
    else if (fixedScript.includes('class ')) detectedType = 'classDiagram';
    else if (fixedScript.includes('state ')) detectedType = 'stateDiagram-v2';
    // Add more detections if necessary

    fixedScript = `${detectedType}\n${fixedScript}`;
    addChange(`Added missing diagram declaration: ${detectedType}`);
  }

  // 3. Normalize common keywords and syntax
  fixedScript = fixedScript.replace(/^graph\b/i, 'flowchart'); // Prefer 'flowchart' over 'graph'

  // Handle common AI-generated syntax issues
  fixedScript = fixedScript.replace(/^graph\s+TD;/i, 'flowchart TD'); // Remove semicolon from declaration
  fixedScript = fixedScript.replace(/^graph\s+LR;/i, 'flowchart LR'); // Remove semicolon from declaration
  fixedScript = fixedScript.replace(/^flowchart\s+TD;/i, 'flowchart TD'); // Remove semicolon from declaration
  fixedScript = fixedScript.replace(/^flowchart\s+LR;/i, 'flowchart LR'); // Remove semicolon from declaration

  const firstLineParts = fixedScript.split('\n')[0].trim().split(' ');
  const actualDirectionToken = firstLineParts[1] ? firstLineParts[1].toUpperCase().split(';')[0] : "";

  if (firstLineParts[0].toLowerCase() === 'flowchart' && firstLineParts.length === 1) {
      fixedScript = fixedScript.replace(/^(flowchart)/i, 'flowchart TD');
      addChange('Added default direction TD to flowchart.');
  } else if (firstLineParts[0].toLowerCase() === 'flowchart' && firstLineParts[1] && actualDirectionToken && !['TD', 'TB', 'LR', 'RL'].includes(actualDirectionToken)) {
      const currentScriptLinesForDirFix = fixedScript.split('\n'); // Use current script state
      fixedScript = `flowchart TD\n${currentScriptLinesForDirFix.slice(1).join('\n')}`; // Reconstruct with current tail
      addChange('Corrected flowchart direction to TD.');
  }

  // Fix common AI-generated arrow syntax issues
  fixedScript = fixedScript.replace(/\s*-\s*-\s*>\s*/g, ' --> '); // Fix spaced arrows
  fixedScript = fixedScript.replace(/\s*=\s*=\s*>\s*/g, ' ==> '); // Fix spaced thick arrows
  fixedScript = fixedScript.replace(/\s*-\s*\.\s*-\s*>\s*/g, ' -.-> '); // Fix spaced dotted arrows

  lines = fixedScript.split('\n'); // Refresh lines array before processing for subgraph on first line

  // 3.25. Ensure subgraph declarations on the first line (after diagram type and semicolon) are moved to a new line
  if (lines.length > 0) {
    let firstLineContent = lines[0];
    const firstLineDeclAndRest = firstLineContent.split(';');
    if (firstLineDeclAndRest.length > 1) {
      const declarationPart = firstLineDeclAndRest[0].trim();
      let restOfFirstLine = firstLineDeclAndRest.slice(1).join(';').trim();

      // Check if the declarationPart is a known diagram type and rest starts with subgraph
      const isKnownDeclaration = DIAGRAM_TYPES.some(type => declarationPart.toLowerCase().startsWith(type.toLowerCase()));
      if (isKnownDeclaration && restOfFirstLine.match(/^subgraph\b/i)) {
        fixedScript = declarationPart + '\n' + restOfFirstLine + (lines.length > 1 ? '\n' + lines.slice(1).join('\n') : '');
        addChange('Moved subgraph declaration from the first line to a new line for proper processing.');
        lines = fixedScript.split('\n'); // Re-split lines as fixedScript has changed
      }
    }
  }

  // 3.5. Ensure subgraph titles with spaces or special characters are quoted
  lines = fixedScript.split('\n'); // Re-split lines as fixedScript might have changed
  fixedScript = lines.map(line => {
    const lineTrimmed = line.trim();
    // Regex to match "subgraph " case-insensitively, and capture the keyword and the title part
    const subgraphMatch = lineTrimmed.match(/^(subgraph\s+)(.*)/i);

    if (subgraphMatch) {
        const keywordPart = subgraphMatch[1]; // e.g., "subgraph "
        let titlePart = subgraphMatch[2].trim(); // The rest of the line, trimmed

        // Case 1: Title is already in `id "quoted_content"` format
        const idWithQuotedTitleMatch = titlePart.match(/^([a-zA-Z0-9_]+)\s+(".*?")$/);
        if (idWithQuotedTitleMatch) {
            const id = idWithQuotedTitleMatch[1];
            let quotedContent = idWithQuotedTitleMatch[2]; // e.g., ""content""
            let innerContent = quotedContent.substring(1, quotedContent.length - 1);
            const escapedInnerContent = escapeStringForMermaid(innerContent);
            if (escapedInnerContent !== innerContent) {
                addChange(`Escaped special characters in subgraph title for ID ${id}: ${innerContent}`);
                return `${keywordPart}${id} "${escapedInnerContent}"`;
            }
            return line; // Original line if no change after escaping
        }

        // Case 2: Title is already a simple quoted string `"quoted_content"`
        const simpleQuotedTitleMatch = titlePart.match(/^(".*?")$/);
        if (simpleQuotedTitleMatch) {
            let quotedContent = simpleQuotedTitleMatch[1];
            let innerContent = quotedContent.substring(1, quotedContent.length - 1);
            const escapedInnerContent = escapeStringForMermaid(innerContent);
            if (escapedInnerContent !== innerContent) {
                addChange(`Escaped special characters in quoted subgraph title: ${innerContent}`);
                return `${keywordPart}"${escapedInnerContent}"`;
            }
            return line; // Original line if no change after escaping
        }

        // Case 3: Title is unquoted. Quote if it contains spaces or problematic characters.
        // Problematic characters include spaces, parentheses, brackets, braces, hyphens (if not part of a simple ID).
        if (titlePart.length > 0 && (!/^[a-zA-Z0-9_]+$/.test(titlePart) || titlePart.includes(' '))) {
            const escapedTitle = escapeStringForMermaid(titlePart);
            if (escapedTitle !== titlePart) {
                 addChange(`Escaped and quoted subgraph title: ${titlePart}`);
            } else {
                 addChange(`Quoted subgraph title: ${titlePart}`);
            }
            return `${keywordPart}"${escapedTitle}"`;
        }
        // If it's an unquoted simple ID (e.g., "subgraph myid"), no change needed by this rule.
    }
    return line;
  }).join('\n');

  // 4. Fix arrow syntax (more comprehensive)
  fixedScript = fixedScript.replace(/\s*-\s*-\s*>/g, ' --> '); // "A -- > B" to "A --> B"
  fixedScript = fixedScript.replace(/\s*-\s*-\s*-\s*>/g, ' --> '); // "A --- > B" to "A --> B"
  fixedScript = fixedScript.replace(/\s*=\s*=\s*>/g, ' ==> '); // "A == > B" to "A ==> B"
  fixedScript = fixedScript.replace(/\s*-\s*\.\s*-\s*>/g, ' -.-> '); // "A -.-> B"
  fixedScript = fixedScript.replace(/\s*-\s*-\s*x\s*/g, ' --x ');
  fixedScript = fixedScript.replace(/\s*x\s*-\s*-\s*/g, ' x-- ');
  fixedScript = fixedScript.replace(/\s*<-\s*-\s*>/g, ' <--> ');

  // 5. Node text: Ensure problematic characters are quoted or handled
  // Regex for node text in square brackets, e.g. id[label text]
  fixedScript = fixedScript.replace(
    /([a-zA-Z0-9_]+)\s*\[\s*([^"'\]][^\r\n]*?[^"'\]]?)\s*\]/g,
    (match: string, nodeId: string, label: string) => {
      const trimmedLabel = label.trim();
      if (trimmedLabel.includes(' ') || /[^a-zA-Z0-9_#(),.:;\-/\]]/.test(trimmedLabel)) {
        const escapedLabel = escapeStringForMermaid(trimmedLabel);
        if (trimmedLabel !== escapedLabel) addChange(`Escaped special characters in label for node ${nodeId}`);
        return `${nodeId}["${escapedLabel}"]`;
      }
      return `${nodeId}[${trimmedLabel}]`;
    }
  );
  // Regex for node text in round brackets, e.g. id(label text)
  fixedScript = fixedScript.replace(
    /([a-zA-Z0-9_]+)\s*\(\s*([^"')][^\r\n]*?[^"')]?)\s*\)/g,
    (match: string, nodeId: string, label: string) => {
      const trimmedLabel = label.trim();
      if (trimmedLabel.includes(' ') || /[^a-zA-Z0-9_#(),.:;\-/\]]/.test(trimmedLabel)) {
        const escapedLabel = escapeStringForMermaid(trimmedLabel);
        if (trimmedLabel !== escapedLabel) addChange(`Escaped special characters in label for node ${nodeId}`);
        return `${nodeId}("${escapedLabel}")`;
      }
      return `${nodeId}(${trimmedLabel})`;
    }
  );
  // Regex for node text in stadium shape, e.g. id{{label text}}
  fixedScript = fixedScript.replace(
    /([a-zA-Z0-9_]+)\s*\{\{\s*([^"'}][^\r\n]*?[^"'}])\s*\}\}/g,
    (match: string, nodeId: string, label: string) => {
      const trimmedLabel = label.trim();
      if (trimmedLabel.includes(' ') || /[^a-zA-Z0-9_#(),.:;\-/\]]/.test(trimmedLabel)) {
        const escapedLabel = escapeStringForMermaid(trimmedLabel);
        if (trimmedLabel !== escapedLabel) addChange(`Escaped special characters in label for node ${nodeId}`);
        return `${nodeId}{{"${escapedLabel}"}}`;
      }
      return `${nodeId}{{${trimmedLabel}}}`; 
    }
  );

  // 6. Subgraphs: Ensure 'end' keyword is present and correctly formatted
  let subgraphDepth = 0;
  // lines is now declared earlier, but we might need to re-split if fixedScript changed significantly before this point.
  // For safety, let's re-assign 'lines' with the current state of fixedScript.
  lines = fixedScript.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].match(/^\s*subgraph\s+.*/i)) {
      subgraphDepth++;
    }
    if (lines[i].match(/^\s*end\b/i)) {
      subgraphDepth--;
    }
  }
  if (subgraphDepth > 0) {
    fixedScript += '\nend'.repeat(subgraphDepth);
    addChange(`Added ${subgraphDepth} missing 'end' keyword(s) for subgraphs.`);
  }

  // 7. Comments: Ensure they are on their own lines or correctly formatted
  fixedScript = fixedScript.split('\n').map(line => {
    const trimmedLine = line.trim();
    if (trimmedLine.startsWith('%%')) return line; // Full line comment
    const parts = line.split('%%');
    if (parts.length > 1 && parts[0].trim() !== '') {
      addChange(`Formatted comment: ${parts[1]}`);
      return parts[0].trim() + ` %% ${parts[1].trim()}`;
    } else if (parts.length > 1 && parts[0].trim() === '') {
      return `%% ${parts[1].trim()}`;
    }
    return line;
  }).join('\n');

  // 8. Remove trailing semicolons from lines (Mermaid is generally tolerant but good for consistency)
  fixedScript = fixedScript.split('\n').map(line => {
    // Avoid removing semicolons inside quoted text
    if (!line.match(/("[^"]*?;[^"]*")|(\'[^\']*?;[^\']*\')/)) {
      return line.replace(/;\s*$/, '');
    }
    return line;
  }).join('\n');

  // 9. Consolidate multiple blank lines
  fixedScript = fixedScript.replace(/\n\s*\n/g, '\n\n');

  // Final check for validity (basic)
  let isValid = true;
  let errorMsg: string | undefined;

  if (fixedScript.length > 15000) { // Arbitrary limit for very long scripts
    isValid = false;
    errorMsg = "Script is too long, may cause performance issues.";
    addChange("Warning: Script is very long.");
  }

  if (originalScriptForChangeDetection !== fixedScript && changes.length === 0) {
    addChange("Applied general formatting improvements.");
  }

  return {
    fixedScript,
    changes,
    isValid,
    error: errorMsg,
  };
}

// Helper to validate if the script is likely a Mermaid script
export function isLikelyMermaidScript(script: string): boolean {
  if (!script || typeof script !== 'string' || script.trim().length < 3) {
    return false;
  }
  const lowerScript = script.toLowerCase();
  const keywords = [
    'flowchart', 'graph', 'subgraph', 'sequenceDiagram', 'participant', 'actor',
    'classDiagram', 'class', 'stateDiagram', 'state', 'gantt', 'section', 'erDiagram',
    'pie', 'gitGraph', 'commit', 'branch', 'merge', 'journey', 'C4Context', 'mindmap', 'timeline',
    '-->', '<--', '---', '==>', '<==', '-.->', '<.-.'
  ];
  return keywords.some(kw => lowerScript.includes(kw));
}

/**
 * Specialized function to fix AI-generated Mermaid scripts
 * This addresses common issues that AI models produce
 */
export function fixAiGeneratedMermaidScript(script: string): MermaidFixResult {
  if (typeof script !== 'string') {
    return {
      fixedScript: '',
      changes: [],
      isValid: false,
      error: 'Invalid input: Script must be a string.',
    };
  }

  let fixedScript = script.replace(/\r\n/g, '\n').trim();
  const changes: string[] = [];
  const addChange = (description: string) => changes.push(description);

  if (!fixedScript) {
    fixedScript = 'flowchart TD\n    A[Start] --> B[End]';
    addChange('Script was empty. Added a default diagram.');
    return { fixedScript, changes, isValid: true };
  }

  // 1. Remove markdown code blocks (common AI mistake)
  if (fixedScript.startsWith('```mermaid')) {
    fixedScript = fixedScript.substring('```mermaid'.length).trim();
    addChange('Removed markdown code block wrapper');
  }
  if (fixedScript.startsWith('```')) {
    fixedScript = fixedScript.substring('```'.length).trim();
    addChange('Removed markdown code block wrapper');
  }
  if (fixedScript.endsWith('```')) {
    fixedScript = fixedScript.substring(0, fixedScript.length - '```'.length).trim();
    addChange('Removed markdown code block wrapper');
  }

  // 2. Fix diagram declaration issues
  const lines = fixedScript.split('\n');
  const firstLine = lines[0].trim();

  // Convert 'graph TD;' to 'flowchart TD' (common AI pattern)
  if (firstLine.match(/^graph\s+(TD|LR|TB|RL);?$/i)) {
    const direction = firstLine.match(/^graph\s+(TD|LR|TB|RL);?$/i)?.[1] || 'TD';
    lines[0] = `flowchart ${direction.toUpperCase()}`;
    fixedScript = lines.join('\n');
    addChange(`Converted 'graph ${direction};' to 'flowchart ${direction.toUpperCase()}'`);
  }

  // Add missing diagram declaration
  if (!firstLine.toLowerCase().startsWith('flowchart') && !firstLine.toLowerCase().startsWith('graph')) {
    fixedScript = `flowchart TD\n${fixedScript}`;
    addChange('Added missing flowchart declaration');
  }

  // 3. Fix common node syntax issues
  // Fix nodes with unquoted labels containing spaces
  fixedScript = fixedScript.replace(/(\w+)\[([^"\]]*\s[^"\]]*)\]/g, (match, nodeId, label) => {
    if (!label.startsWith('"') && !label.endsWith('"')) {
      addChange(`Quoted label for node ${nodeId}: "${label}"`);
      return `${nodeId}["${label}"]`;
    }
    return match;
  });

  // 4. Fix arrow syntax issues (AI often generates malformed arrows)
  const arrowFixes = [
    { pattern: /\s*-\s*-\s*>\s*/g, replacement: ' --> ', desc: 'Fixed spaced solid arrows' },
    { pattern: /\s*=\s*=\s*>\s*/g, replacement: ' ==> ', desc: 'Fixed spaced thick arrows' },
    { pattern: /\s*-\s*\.\s*-\s*>\s*/g, replacement: ' -.-> ', desc: 'Fixed spaced dotted arrows' },
    { pattern: /\s*<\s*-\s*-\s*>\s*/g, replacement: ' <--> ', desc: 'Fixed bidirectional arrows' },
    { pattern: /-->/g, replacement: ' --> ', desc: 'Added spacing around arrows' },
    { pattern: /==>/g, replacement: ' ==> ', desc: 'Added spacing around thick arrows' },
    { pattern: /-\.->/g, replacement: ' -.-> ', desc: 'Added spacing around dotted arrows' }
  ];

  arrowFixes.forEach(fix => {
    const before = fixedScript;
    fixedScript = fixedScript.replace(fix.pattern, fix.replacement);
    if (before !== fixedScript) {
      addChange(fix.desc);
    }
  });

  // 5. Remove trailing semicolons (modern Mermaid doesn't need them)
  const beforeSemicolon = fixedScript;
  fixedScript = fixedScript.split('\n').map(line => {
    // Don't remove semicolons inside quoted strings
    if (!line.includes('"')) {
      return line.replace(/;\s*$/, '');
    }
    return line;
  }).join('\n');
  if (beforeSemicolon !== fixedScript) {
    addChange('Removed trailing semicolons');
  }

  // 6. Fix subgraph issues
  const subgraphCount = (fixedScript.match(/subgraph\s+/gi) || []).length;
  const endCount = (fixedScript.match(/^\s*end\s*$/gm) || []).length;
  if (subgraphCount > endCount) {
    const missingEnds = subgraphCount - endCount;
    fixedScript += '\n' + 'end\n'.repeat(missingEnds).trim();
    addChange(`Added ${missingEnds} missing 'end' statement(s) for subgraphs`);
  }

  // 7. Clean up extra whitespace
  fixedScript = fixedScript.replace(/\n\s*\n\s*\n/g, '\n\n'); // Remove excessive blank lines
  fixedScript = fixedScript.trim();

  // Basic validation
  const isValid = validateMermaidScript(fixedScript);

  return {
    fixedScript,
    changes,
    isValid,
    error: isValid ? undefined : 'Script may still contain syntax errors'
  };
}

/**
 * Basic validation for Mermaid scripts
 */
function validateMermaidScript(script: string): boolean {
  if (!script || script.trim().length < 5) return false;

  const lines = script.split('\n').map(l => l.trim()).filter(l => l.length > 0);
  if (lines.length < 1) return false;

  // Check for valid diagram declaration
  const firstLine = lines[0].toLowerCase();
  const validDeclarations = ['flowchart', 'graph', 'sequencediagram', 'classdiagram', 'statediagram'];
  const hasValidDeclaration = validDeclarations.some(decl => firstLine.startsWith(decl));

  if (!hasValidDeclaration) return false;

  // Check for at least one meaningful content line
  const hasContent = lines.slice(1).some(line =>
    line.includes('-->') ||
    line.includes('[') ||
    line.includes('(') ||
    line.includes('{') ||
    line.includes('subgraph')
  );

  return hasContent;
}

